#!/usr/bin/env python3
"""
Script de pruebas para los microservicios FastAPI
Simula las llamadas HTTP que harían las aplicaciones frontend
"""

import json
import time
from datetime import datetime

def print_header(service_name):
    print(f"\n{'='*50}")
    print(f"PROBANDO {service_name}")
    print(f"{'='*50}")

def print_endpoint(method, endpoint, description):
    print(f"\n🔍 {method} {endpoint}")
    print(f"   {description}")

def simulate_response(data, status_code=200):
    print(f"   ✅ Status: {status_code}")
    print(f"   📄 Response: {json.dumps(data, indent=2, ensure_ascii=False)}")

def test_user_service():
    print_header("USER SERVICE (Puerto 3001)")
    
    # Health check
    print_endpoint("GET", "/health", "Verificar estado del servicio")
    simulate_response({"status": "healthy", "service": "user-service"})
    
    # Crear perfil de usuario
    print_endpoint("POST", "/users/profile", "Crear perfil de usuario")
    user_data = {
        "user_id": "user_123",
        "email": "<EMAIL>",
        "name": "<PERSON>",
        "phone": "+**********",
        "address": "Calle Principal 123"
    }
    simulate_response(user_data, 201)
    
    # Obtener perfil de usuario
    print_endpoint("GET", "/users/user_123/profile", "Obtener perfil de usuario")
    simulate_response(user_data)
    
    # Actualizar perfil de usuario
    print_endpoint("PUT", "/users/user_123/profile", "Actualizar perfil de usuario")
    updated_data = {**user_data, "phone": "+**********"}
    simulate_response(updated_data)

def test_menu_service():
    print_header("MENU SERVICE (Puerto 3002)")
    
    # Health check
    print_endpoint("GET", "/health", "Verificar estado del servicio")
    simulate_response({"status": "healthy", "service": "menu-service"})
    
    # Crear item del menú
    print_endpoint("POST", "/menu/items", "Crear item del menú")
    menu_item = {
        "id": "item_001",
        "name": "Hamburguesa Clásica",
        "description": "Hamburguesa con carne, lechuga, tomate y queso",
        "price": 12.99,
        "category": "Hamburguesas",
        "available": True,
        "image_url": "https://example.com/burger.jpg"
    }
    simulate_response(menu_item, 201)
    
    # Obtener items del menú
    print_endpoint("GET", "/menu/items", "Obtener todos los items del menú")
    simulate_response([menu_item])
    
    # Obtener items disponibles
    print_endpoint("GET", "/menu/items?available_only=true", "Obtener solo items disponibles")
    simulate_response([menu_item])
    
    # Crear promoción
    print_endpoint("POST", "/promotions", "Crear promoción")
    promotion = {
        "id": "promo_001",
        "name": "Descuento de Verano",
        "description": "20% de descuento en hamburguesas",
        "discount_percentage": 20.0,
        "menu_item_ids": ["item_001"],
        "active": True,
        "start_date": "2024-06-01T00:00:00",
        "end_date": "2024-08-31T23:59:59"
    }
    simulate_response(promotion, 201)
    
    # Obtener promociones activas
    print_endpoint("GET", "/promotions?active_only=true", "Obtener promociones activas")
    simulate_response([promotion])

def test_order_service():
    print_header("ORDER SERVICE (Puerto 3003)")
    
    # Health check
    print_endpoint("GET", "/health", "Verificar estado del servicio")
    simulate_response({"status": "healthy", "service": "order-service"})
    
    # Agregar item al carrito
    print_endpoint("POST", "/cart/user_123/items", "Agregar item al carrito")
    cart_item = {
        "menu_item_id": "item_001",
        "quantity": 2,
        "customizations": {"sin_cebolla": "true", "extra_queso": "true"}
    }
    simulate_response({"message": "Item added to cart"})
    
    # Obtener carrito
    print_endpoint("GET", "/cart/user_123", "Obtener carrito del usuario")
    cart = {
        "user_id": "user_123",
        "items": [cart_item],
        "updated_at": datetime.now().isoformat()
    }
    simulate_response(cart)
    
    # Crear pedido
    print_endpoint("POST", "/orders", "Crear nuevo pedido")
    order = {
        "id": "order_001",
        "user_id": "user_123",
        "items": [{
            "menu_item_id": "item_001",
            "quantity": 2,
            "customizations": {"sin_cebolla": "true", "extra_queso": "true"},
            "unit_price": 12.99
        }],
        "total_amount": 25.98,
        "status": "pendiente",
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    simulate_response(order, 201)
    
    # Obtener pedidos
    print_endpoint("GET", "/orders", "Obtener todos los pedidos")
    simulate_response([order])
    
    # Actualizar estado del pedido
    print_endpoint("PUT", "/orders/order_001/status", "Actualizar estado del pedido")
    simulate_response({"message": "Order status updated to preparando"})

def test_payment_service():
    print_header("PAYMENT SERVICE (Puerto 3004)")
    
    # Health check
    print_endpoint("GET", "/health", "Verificar estado del servicio")
    simulate_response({"status": "healthy", "service": "payment-service"})
    
    # Crear pago
    print_endpoint("POST", "/payments", "Procesar pago")
    payment = {
        "id": "payment_001",
        "order_id": "order_001",
        "user_id": "user_123",
        "amount": 25.98,
        "payment_method": "tarjeta_credito",
        "status": "procesando",
        "transaction_id": "txn_123456",
        "payment_details": {"card_last_four": "1234"},
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    simulate_response(payment, 201)
    
    # Obtener pago
    print_endpoint("GET", "/payments/payment_001", "Obtener información del pago")
    simulate_response(payment)
    
    # Confirmar pago
    print_endpoint("POST", "/payments/payment_001/confirm", "Confirmar pago")
    simulate_response({"message": "Payment completado"})
    
    # Obtener pagos por pedido
    print_endpoint("GET", "/payments/order/order_001", "Obtener pagos por pedido")
    simulate_response([{**payment, "status": "completado"}])

def main():
    print("🚀 SIMULACIÓN DE PRUEBAS DE MICROSERVICIOS FASTAPI")
    print("=" * 60)
    print("Esta simulación muestra cómo funcionarían los endpoints")
    print("cuando los servicios estén ejecutándose en localhost")
    
    test_user_service()
    test_menu_service()
    test_order_service()
    test_payment_service()
    
    print(f"\n{'='*60}")
    print("✅ SIMULACIÓN COMPLETADA")
    print("📋 RESUMEN DE SERVICIOS:")
    print("   • User Service:    http://localhost:3001")
    print("   • Menu Service:    http://localhost:3002") 
    print("   • Order Service:   http://localhost:3003")
    print("   • Payment Service: http://localhost:3004")
    print("\n🔧 Para ejecutar realmente:")
    print("   1. Instalar: pip install fastapi uvicorn")
    print("   2. Ejecutar cada servicio: python main.py")
    print("   3. Probar con: curl http://localhost:3001/health")

if __name__ == "__main__":
    main()
