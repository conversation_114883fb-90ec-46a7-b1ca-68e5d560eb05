#!/usr/bin/env python3
"""
Script para probar que los servicios FastAPI funcionen correctamente
"""

import sys
import os

def test_import_service(service_path, service_name):
    """Prueba que un servicio se pueda importar sin errores"""
    print(f"\n🔍 Probando {service_name}...")
    
    try:
        # Cambiar al directorio del servicio
        original_dir = os.getcwd()
        os.chdir(service_path)
        
        # Agregar el directorio al path de Python
        sys.path.insert(0, service_path)
        
        # Intentar importar el módulo
        import main
        
        # Verificar que la app de FastAPI existe
        if hasattr(main, 'app'):
            print(f"   ✅ {service_name} importado correctamente")
            print(f"   📋 Endpoints disponibles:")
            
            # Mostrar las rutas disponibles
            for route in main.app.routes:
                if hasattr(route, 'methods') and hasattr(route, 'path'):
                    methods = ', '.join(route.methods)
                    print(f"      {methods} {route.path}")
            
            return True
        else:
            print(f"   ❌ {service_name}: No se encontró la app FastAPI")
            return False
            
    except ImportError as e:
        print(f"   ❌ {service_name}: Error de importación - {e}")
        return False
    except Exception as e:
        print(f"   ❌ {service_name}: Error - {e}")
        return False
    finally:
        # Restaurar directorio original
        os.chdir(original_dir)
        # Limpiar el path
        if service_path in sys.path:
            sys.path.remove(service_path)

def main():
    print("🚀 VERIFICACIÓN DE SERVICIOS FASTAPI")
    print("=" * 50)
    
    # Definir servicios a probar
    services = [
        ("backend/user-service", "User Service"),
        ("backend/menu-service", "Menu Service"),
        ("backend/order-service", "Order Service"),
        ("backend/payment-service", "Payment Service")
    ]
    
    results = []
    
    for service_path, service_name in services:
        if os.path.exists(service_path):
            success = test_import_service(service_path, service_name)
            results.append((service_name, success))
        else:
            print(f"\n❌ {service_name}: Directorio no encontrado - {service_path}")
            results.append((service_name, False))
    
    # Resumen
    print(f"\n{'='*50}")
    print("📊 RESUMEN DE VERIFICACIÓN:")
    
    successful = 0
    for service_name, success in results:
        status = "✅ OK" if success else "❌ ERROR"
        print(f"   {service_name}: {status}")
        if success:
            successful += 1
    
    print(f"\n🎯 Servicios funcionando: {successful}/{len(results)}")
    
    if successful == len(results):
        print("\n🎉 ¡TODOS LOS SERVICIOS ESTÁN LISTOS!")
        print("\n🔧 Para ejecutar los servicios:")
        print("   Terminal 1: cd backend/user-service && python main.py")
        print("   Terminal 2: cd backend/menu-service && python main.py")
        print("   Terminal 3: cd backend/order-service && python main.py")
        print("   Terminal 4: cd backend/payment-service && python main.py")
        print("\n🌐 URLs de prueba:")
        print("   http://localhost:3001/docs (User Service)")
        print("   http://localhost:3002/docs (Menu Service)")
        print("   http://localhost:3003/docs (Order Service)")
        print("   http://localhost:3004/docs (Payment Service)")
    else:
        print(f"\n⚠️  Hay {len(results) - successful} servicios con problemas")
        print("   Revisa los errores mostrados arriba")

if __name__ == "__main__":
    main()
