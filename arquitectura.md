# Arquitectura SaaS - Pedidos de Comida Rápida

## Microservicios Core

### 1. **User Service**
- **Responsabilidad**: Gestión de clientes
- **Funciones**:
  - Registro/login de clientes
  - Perfil básico del usuario
- **Base de datos**: PostgreSQL
- **Puerto**: 3001

### 2. **Menu Service**
- **Responsabilidad**: Gestión del menú y promociones
- **Funciones**:
  - CRUD de productos del menú
  - Gestión de promociones activas
  - Precios y disponibilidad
- **Base de datos**: PostgreSQL
- **Puerto**: 3002

### 3. **Order Service**
- **Responsabilidad**: Procesamiento de pedidos
- **Funciones**:
  - Crear pedido
  - Personalización de productos
  - Gestión del carrito
  - Estados del pedido (pendiente, preparando, listo)
- **Base de datos**: PostgreSQL
- **Puerto**: 3003

### 4. **Payment Service**
- **Responsabilidad**: Procesamiento de pagos
- **Funciones**:
  - Integración con pasarelas de pago
  - Confirmación de pagos
  - Estados de transacciones
- **Base de datos**: PostgreSQL
- **Puerto**: 3004

## Interfaces de Usuario

### **Dashboard Administrador**
- **Tecnología**: Next.js
- **Funcionalidades**:
  - Lista de pedidos en tiempo real
  - Estado de preparación (pendiente → preparando → listo)
  - Datos básicos del pedido (cliente, productos, total)
  - Actualización manual de estados

### **App Cliente**
- **Tecnología**: Next.js
- **Funcionalidades**:
  - Menú de productos
  - Visualización de promociones
  - Personalización de pedidos
  - Carrito de compras
  - Métodos de pago
  - Confirmación de pedido

## Arquitectura Técnica

### **API Gateway**
- **Tecnología**: Kong/Nginx
- **Puerto**: 8080
- **Funciones**:
  - Enrutamiento de peticiones
  - Autenticación Clerk
  - Rate limiting

### **Message Broker**
- **Tecnología**: Redis/RabbitMQ
- **Funciones**:
  - Comunicación entre microservicios
  - Notificaciones en tiempo real
  - Cache de sesiones

### **Base de Datos**
- **PostgreSQL**: Para cada microservicio
- **Redis**: Cache y sesiones
- **Adminer**: Interfaz web para gestión de PostgreSQL

## Flujo de Datos Básico

```
Cliente → API Gateway → Microservicio → Base de Datos
                    ↓
            Message Broker → Dashboard Admin
```

## Stack Tecnológico

### **Backend**
- FastAPI (Python)
- PostgreSQL
- Redis
- Clerk para autenticación

### **Frontend**
- Next.js (Dashboard Admin)
- Next.js (App Cliente)
- Tailwind CSS

### **DevOps**
- Docker para contenedores
- Docker Compose para desarrollo local

## Estructura de Despliegue

### **Contenedores Docker**
- user-service (FastAPI)
- menu-service (FastAPI)
- order-service (FastAPI)
- payment-service (FastAPI)
- api-gateway
- postgresql
- redis
- adminer (Gestión de BD)
- client-app (Next.js)
- admin-dashboard (Next.js)

## Comunicación Entre Servicios

### **Síncrona**
- HTTP/REST entre cliente y API Gateway
- HTTP/REST entre API Gateway y microservicios

### **Asíncrona**
- Message Broker para:
  - Actualización de estados de pedidos
  - Notificaciones al dashboard
  - Confirmaciones de pago

## Consideraciones de Seguridad

- Autenticación con Clerk
- Validación de datos en cada microservicio FastAPI
- HTTPS en todas las comunicaciones
- Variables de entorno para credenciales

## Escalabilidad

- Cada microservicio puede escalarse independientemente
- Base de datos separada por servicio
- Cache con Redis para consultas frecuentes
- Load balancer en API Gateway