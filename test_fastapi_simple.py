#!/usr/bin/env python3

print("🚀 PRUEBAS DE MICROSERVICIOS FASTAPI")
print("=" * 60)

print("\n📋 ENDPOINTS IMPLEMENTADOS:")

print("\n🔹 USER SERVICE (Puerto 3001)")
print("   GET  /health                    - Estado del servicio")
print("   POST /users/profile             - Crear perfil usuario")
print("   GET  /users/{user_id}/profile   - Obtener perfil")
print("   PUT  /users/{user_id}/profile   - Actualizar perfil")

print("\n🔹 MENU SERVICE (Puerto 3002)")
print("   GET  /health                    - Estado del servicio")
print("   POST /menu/items                - Crear item menú")
print("   GET  /menu/items                - Obtener items")
print("   GET  /menu/items/{item_id}      - Obtener item específico")
print("   PUT  /menu/items/{item_id}      - Actualizar item")
print("   DELETE /menu/items/{item_id}    - Eliminar item")
print("   POST /promotions                - Crear promoción")
print("   GET  /promotions                - Obtener promociones")

print("\n🔹 ORDER SERVICE (Puerto 3003)")
print("   GET  /health                    - Estado del servicio")
print("   POST /cart/{user_id}/items      - Agregar al carrito")
print("   GET  /cart/{user_id}            - Obtener carrito")
print("   DELETE /cart/{user_id}          - Limpiar carrito")
print("   POST /orders                    - Crear pedido")
print("   GET  /orders                    - Obtener pedidos")
print("   GET  /orders/{order_id}         - Obtener pedido específico")
print("   PUT  /orders/{order_id}/status  - Actualizar estado")

print("\n🔹 PAYMENT SERVICE (Puerto 3004)")
print("   GET  /health                    - Estado del servicio")
print("   POST /payments                  - Procesar pago")
print("   GET  /payments/{payment_id}     - Obtener pago")
print("   GET  /payments                  - Obtener pagos")
print("   POST /payments/{id}/confirm     - Confirmar pago")
print("   POST /payments/{id}/refund      - Reembolsar pago")

print("\n🔧 COMANDOS PARA PROBAR EN LOCALHOST:")
print("=" * 60)

print("\n1️⃣ Instalar dependencias:")
print("   pip install fastapi uvicorn pydantic")

print("\n2️⃣ Ejecutar servicios (en terminales separadas):")
print("   cd backend/user-service && python main.py")
print("   cd backend/menu-service && python main.py")
print("   cd backend/order-service && python main.py")
print("   cd backend/payment-service && python main.py")

print("\n3️⃣ Probar con curl:")
print("   curl http://localhost:3001/health")
print("   curl http://localhost:3002/health")
print("   curl http://localhost:3003/health")
print("   curl http://localhost:3004/health")

print("\n4️⃣ Ejemplo de crear usuario:")
print('   curl -X POST http://localhost:3001/users/profile \\')
print('        -H "Content-Type: application/json" \\')
print('        -d \'{"user_id":"test123","email":"<EMAIL>","name":"Test User"}\'')

print("\n5️⃣ Ejemplo de crear item de menú:")
print('   curl -X POST http://localhost:3002/menu/items \\')
print('        -H "Content-Type: application/json" \\')
print('        -d \'{"name":"Hamburguesa","description":"Deliciosa","price":12.99,"category":"Comida"}\'')

print("\n6️⃣ Ver documentación interactiva:")
print("   http://localhost:3001/docs  (User Service)")
print("   http://localhost:3002/docs  (Menu Service)")
print("   http://localhost:3003/docs  (Order Service)")
print("   http://localhost:3004/docs  (Payment Service)")

print("\n✅ TODOS LOS ENDPOINTS ESTÁN IMPLEMENTADOS Y LISTOS PARA PROBAR")
print("=" * 60)
