from fastapi import FastAP<PERSON>, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict
from datetime import datetime
from enum import Enum
import uuid
import os
import json
import redis
import asyncio
import redis.asyncio as aioredis
from sqlalchemy import create_engine, Column, String, Float, DateTime, Enum as SAEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import sessionmaker, Session, declarative_base
from dotenv import load_dotenv

load_dotenv()

# --- Configuration ---
DATABASE_URL = os.getenv("DATABASE_URL", "**************************************************")
REDIS_URL = os.getenv("REDIS_URL", "redis://redis:6379/0")

# --- Database Setup ---
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# --- Redis Setup ---
redis_pool = redis.ConnectionPool.from_url(REDIS_URL, decode_responses=True)


# --- WebSocket Connection Manager ---
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)

manager = ConnectionManager()

# --- Redis Pub/Sub Listener ---
async def redis_listener():
    r = aioredis.from_url(REDIS_URL, decode_responses=True)
    pubsub = r.pubsub()
    await pubsub.subscribe("orders_channel")
    print("Listening for Redis messages on orders_channel for WebSocket broadcast...")
    while True:
        try:
            message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)
            if message:
                await manager.broadcast(message['data'])
            await asyncio.sleep(0.01)
        except Exception as e:
            print(f"Redis listener error: {e}")
            await asyncio.sleep(5)


@app.on_event("startup")
async def startup_event():
    asyncio.create_task(redis_listener())

# --- Enums ---
class OrderStatus(str, Enum):
    PENDING = "pendiente"
    PREPARING = "preparando"
    READY = "listo"
    DELIVERED = "entregado"
    CANCELLED = "cancelado"

# --- SQLAlchemy Models ---
class OrderDB(Base):
    __tablename__ = "orders"
    id = Column(String, primary_key=True, index=True)
    user_id = Column(String, index=True)
    items = Column(JSONB)
    total_amount = Column(Float)
    status = Column(SAEnum(OrderStatus), default=OrderStatus.PENDING)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

Base.metadata.create_all(bind=engine)

# --- Pydantic Models ---
class OrderItem(BaseModel):
    menu_item_id: str
    quantity: int
    customizations: Optional[Dict[str, str]] = {}
    unit_price: float

class Order(BaseModel):
    id: str
    user_id: str
    items: List[OrderItem]
    total_amount: float
    status: OrderStatus
    created_at: datetime
    updated_at: datetime
    class Config: orm_mode = True

class OrderCreate(BaseModel):
    user_id: str
    items: List[OrderItem]

class CartItem(BaseModel):
    menu_item_id: str
    quantity: int
    customizations: Optional[Dict[str, str]] = {}

class Cart(BaseModel):
    user_id: str
    items: List[CartItem]
    updated_at: datetime

# --- Dependencies ---
def get_db():
    db = SessionLocal()
    try: yield db
    finally: db.close()

def get_redis_sync():
    # Using the synchronous redis client for regular endpoints
    return __import__("redis").from_url(REDIS_URL, decode_responses=True)

app = FastAPI(title="Order Service", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "order-service"}


# --- WebSocket Endpoint ---
@app.websocket("/ws/orders")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            await websocket.receive_text() # Keep connection open
    except WebSocketDisconnect:
        manager.disconnect(websocket)


# --- Cart Management (Redis) ---
@app.post("/cart/{user_id}/items")
async def add_to_cart(user_id: str, item: CartItem, r: redis.Redis = Depends(get_redis_sync)):
    cart_key = f"cart:{user_id}"
    cart_data = r.get(cart_key)
    cart = Cart.model_validate_json(cart_data) if cart_data else Cart(user_id=user_id, items=[], updated_at=datetime.utcnow())
    
    cart.items.append(item)
    cart.updated_at = datetime.utcnow()
    r.set(cart_key, cart.model_dump_json())
    return {"message": "Item added to cart"}

@app.get("/cart/{user_id}", response_model=Cart)
async def get_cart(user_id: str, r: redis.Redis = Depends(get_redis_sync)):
    cart_key = f"cart:{user_id}"
    cart_data = r.get(cart_key)
    if not cart_data:
        return Cart(user_id=user_id, items=[], updated_at=datetime.utcnow())
    return Cart.model_validate_json(cart_data)

@app.delete("/cart/{user_id}")
async def clear_cart(user_id: str, r: redis.Redis = Depends(get_redis_sync)):
    r.delete(f"cart:{user_id}")
    return {"message": "Cart cleared"}

# --- Order Management (PostgreSQL) ---
@app.post("/orders", response_model=Order)
async def create_order(order_create: OrderCreate, db: Session = Depends(get_db), r: redis.Redis = Depends(get_redis_sync)):
    order_id = str(uuid.uuid4())
    total_amount = sum(item.unit_price * item.quantity for item in order_create.items)
    items_data = [item.model_dump() for item in order_create.items]

    db_order = OrderDB(
        id=order_id, user_id=order_create.user_id, items=items_data,
        total_amount=total_amount, status=OrderStatus.PENDING
    )
    db.add(db_order)
    db.commit()
    db.refresh(db_order)

    order_model = Order.from_orm(db_order)
    
    # Publish event to Redis
    event_data = json.dumps({"event": "order_created", "order": order_model.model_dump(mode='json')})
    r.publish("orders_channel", event_data)

    # Clear cart after order creation
    clear_cart(order_create.user_id, r)
    return order_model

@app.get("/orders", response_model=List[Order])
async def get_orders(user_id: Optional[str] = None, status: Optional[OrderStatus] = None, db: Session = Depends(get_db)):
    query = db.query(OrderDB).order_by(OrderDB.created_at.desc())
    if user_id:
        query = query.filter(OrderDB.user_id == user_id)
    if status:
        query = query.filter(OrderDB.status == status)
    return query.all()

@app.get("/orders/{order_id}", response_model=Order)
async def get_order(order_id: str, db: Session = Depends(get_db)):
    db_order = db.query(OrderDB).filter(OrderDB.id == order_id).first()
    if not db_order:
        raise HTTPException(status_code=404, detail="Order not found")
    return db_order

@app.put("/orders/{order_id}/status", response_model=Order)
async def update_order_status(order_id: str, status: OrderStatus, db: Session = Depends(get_db), r: redis.Redis = Depends(get_redis_sync)):
    db_order = db.query(OrderDB).filter(OrderDB.id == order_id).first()
    if not db_order:
        raise HTTPException(status_code=404, detail="Order not found")
    
    db_order.status = status
    db_order.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_order)

    order_model = Order.from_orm(db_order)

    # Publish event to Redis
    event_data = json.dumps({"event": "order_status_updated", "order": order_model.model_dump(mode='json')})
    r.publish("orders_channel", event_data)

    return order_model

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3003)