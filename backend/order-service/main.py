from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict
from datetime import datetime
from enum import Enum
import uuid

app = FastAPI(title="Order Service", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Enums
class OrderStatus(str, Enum):
    PENDING = "pendiente"
    PREPARING = "preparando"
    READY = "listo"
    DELIVERED = "entregado"
    CANCELLED = "cancelado"

# Pydantic Models
class OrderItem(BaseModel):
    menu_item_id: str
    quantity: int
    customizations: Optional[Dict[str, str]] = {}
    unit_price: float

class Order(BaseModel):
    id: str
    user_id: str
    items: List[OrderItem]
    total_amount: float
    status: OrderStatus = OrderStatus.PENDING
    created_at: datetime
    updated_at: datetime

class OrderCreate(BaseModel):
    user_id: str
    items: List[OrderItem]

class CartItem(BaseModel):
    menu_item_id: str
    quantity: int
    customizations: Optional[Dict[str, str]] = {}

class Cart(BaseModel):
    user_id: str
    items: List[CartItem]
    updated_at: datetime

# In-memory storage
orders_db = {}
carts_db = {}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "order-service"}

# Cart Management
@app.post("/cart/{user_id}/items")
async def add_to_cart(user_id: str, item: CartItem):
    if user_id not in carts_db:
        carts_db[user_id] = Cart(user_id=user_id, items=[], updated_at=datetime.now()).model_dump()

    cart = carts_db[user_id]
    cart["items"].append(item.model_dump())
    cart["updated_at"] = datetime.now()
    
    return {"message": "Item added to cart"}

@app.get("/cart/{user_id}", response_model=Cart)
async def get_cart(user_id: str):
    if user_id not in carts_db:
        return Cart(user_id=user_id, items=[], updated_at=datetime.now())
    
    return Cart(**carts_db[user_id])

@app.delete("/cart/{user_id}")
async def clear_cart(user_id: str):
    if user_id in carts_db:
        del carts_db[user_id]
    return {"message": "Cart cleared"}

# Order Management
@app.post("/orders", response_model=Order)
async def create_order(order_create: OrderCreate):
    order_id = str(uuid.uuid4())
    
    # Calculate total amount
    total_amount = sum(item.unit_price * item.quantity for item in order_create.items)
    
    order = Order(
        id=order_id,
        user_id=order_create.user_id,
        items=order_create.items,
        total_amount=total_amount,
        status=OrderStatus.PENDING,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    orders_db[order_id] = order.model_dump()
    
    # Clear user's cart after order creation
    if order_create.user_id in carts_db:
        del carts_db[order_create.user_id]
    
    return order

@app.get("/orders", response_model=List[Order])
async def get_orders(user_id: Optional[str] = None, status: Optional[OrderStatus] = None):
    orders = list(orders_db.values())
    
    if user_id:
        orders = [order for order in orders if order["user_id"] == user_id]
    
    if status:
        orders = [order for order in orders if order["status"] == status]
    
    return [Order(**order) for order in orders]

@app.get("/orders/{order_id}", response_model=Order)
async def get_order(order_id: str):
    if order_id not in orders_db:
        raise HTTPException(status_code=404, detail="Order not found")
    
    return Order(**orders_db[order_id])

@app.put("/orders/{order_id}/status")
async def update_order_status(order_id: str, status: OrderStatus):
    if order_id not in orders_db:
        raise HTTPException(status_code=404, detail="Order not found")
    
    orders_db[order_id]["status"] = status
    orders_db[order_id]["updated_at"] = datetime.now()
    
    return {"message": f"Order status updated to {status}"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3003)
