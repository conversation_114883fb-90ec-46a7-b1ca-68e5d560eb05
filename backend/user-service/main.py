from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import os

app = FastAPI(title="User Service", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic Models
class UserProfile(BaseModel):
    user_id: str
    email: str
    name: str
    phone: Optional[str] = None
    address: Optional[str] = None

class UserProfileUpdate(BaseModel):
    name: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None

# In-memory storage (replace with PostgreSQL in production)
users_db = {}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "user-service"}

@app.post("/users/profile", response_model=UserProfile)
async def create_user_profile(profile: UserProfile):
    """Create user profile after Clerk authentication"""
    if profile.user_id in users_db:
        raise HTTPException(status_code=400, detail="User profile already exists")
    
    users_db[profile.user_id] = profile.model_dump()
    return profile

@app.get("/users/{user_id}/profile", response_model=UserProfile)
async def get_user_profile(user_id: str):
    """Get user profile by user_id"""
    if user_id not in users_db:
        raise HTTPException(status_code=404, detail="User profile not found")
    
    return UserProfile(**users_db[user_id])

@app.put("/users/{user_id}/profile", response_model=UserProfile)
async def update_user_profile(user_id: str, profile_update: UserProfileUpdate):
    """Update user profile"""
    if user_id not in users_db:
        raise HTTPException(status_code=404, detail="User profile not found")
    
    current_profile = users_db[user_id]
    update_data = profile_update.model_dump(exclude_unset=True)
    
    for field, value in update_data.items():
        current_profile[field] = value
    
    users_db[user_id] = current_profile
    return UserProfile(**current_profile)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3001)
