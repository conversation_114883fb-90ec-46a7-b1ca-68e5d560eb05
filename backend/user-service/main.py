from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import os
from sqlalchemy import create_engine, Column, String, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from dotenv import load_dotenv
from clerk_backend_api import Clerk
from clerk_backend_api.models import SDKError

load_dotenv()

# Database Configuration
DATABASE_URL = os.getenv("DATABASE_URL", "**************************************************")

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# SQLAlchemy Model
class UserProfileDB(Base):
    __tablename__ = "user_profiles"
    user_id = Column(String, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    name = Column(String)
    phone = Column(String, nullable=True)
    address = Column(Text, nullable=True)

Base.metadata.create_all(bind=engine)

app = FastAPI(title="User Service", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic Models
class UserProfile(BaseModel):
    user_id: str
    email: str
    name: str
    phone: Optional[str] = None
    address: Optional[str] = None

    class Config:
        orm_mode = True

class UserProfileUpdate(BaseModel):
    name: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None

# Clerk Configuration
CLERK_SECRET_KEY = os.getenv("CLERK_SECRET_KEY")
if not CLERK_SECRET_KEY:
    raise ValueError("CLERK_SECRET_KEY environment variable not set")

clerk_client = Clerk(bearer_auth=CLERK_SECRET_KEY)

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Dependency to verify Clerk session
async def verify_clerk_session(request: Request):
    session_token = request.headers.get("Authorization")
    if not session_token or not session_token.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Unauthorized: Missing or invalid Authorization header")
    
    session_token = session_token.split(" ")[1]
    
    try:
        session = await clerk_client.sessions.verify_session(session_token)
        return session
    except SDKError as e:
        raise HTTPException(status_code=401, detail=f"Unauthorized: {e.errors[0].message}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {e}")

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "user-service"}

@app.post("/users/profile", response_model=UserProfile)
async def create_user_profile(profile: UserProfile, db: Session = Depends(get_db), session: dict = Depends(verify_clerk_session)):
    if profile.user_id != session["user_id"]:
        raise HTTPException(status_code=403, detail="Forbidden: User ID in profile does not match authenticated user")

    db_user = db.query(UserProfileDB).filter(UserProfileDB.user_id == profile.user_id).first()
    if db_user:
        raise HTTPException(status_code=400, detail="User profile already exists")
    
    new_profile = UserProfileDB(**profile.model_dump())
    db.add(new_profile)
    db.commit()
    db.refresh(new_profile)
    return new_profile

@app.get("/users/{user_id}/profile", response_model=UserProfile)
async def get_user_profile(user_id: str, db: Session = Depends(get_db), session: dict = Depends(verify_clerk_session)):
    if user_id != session["user_id"]:
        raise HTTPException(status_code=403, detail="Forbidden: Cannot access other user's profile")

    db_user = db.query(UserProfileDB).filter(UserProfileDB.user_id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User profile not found")
    return db_user

@app.put("/users/{user_id}/profile", response_model=UserProfile)
async def update_user_profile(user_id: str, profile_update: UserProfileUpdate, db: Session = Depends(get_db), session: dict = Depends(verify_clerk_session)):
    if user_id != session["user_id"]:
        raise HTTPException(status_code=403, detail="Forbidden: Cannot update other user's profile")

    db_user = db.query(UserProfileDB).filter(UserProfileDB.user_id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User profile not found")
    
    update_data = profile_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_user, key, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3001)

