from fastapi import FastAPI
import redis
import asyncio
import json
import os
from datetime import datetime

app = FastAPI(title="Notification Service", version="1.0.0")

REDIS_URL = os.getenv("REDIS_URL", "redis://redis:6379/0")
r = redis.Redis.from_url(REDIS_URL, decode_responses=True)

@app.on_event("startup")
async def startup_event():
    asyncio.create_task(redis_listener())

async def redis_listener():
    pubsub = r.pubsub()
    pubsub.subscribe("orders_channel")
    print("Listening for Redis messages on orders_channel...")
    for message in pubsub.listen():
        if message['type'] == 'message':
            data = json.loads(message['data'])
            handle_notification(data)

def handle_notification(data):
    event = data.get("event")
    timestamp = datetime.utcnow().isoformat()
    print(f"--- [Notification Received at {timestamp}] ---")
    
    if event == "order_created":
        order_id = data.get("order_id")
        user_id = data.get("user_id")
        total = data.get("total_amount")
        print(f"[EVENT]: Order Created")
        print(f"  - Order ID: {order_id}")
        print(f"  - User ID: {user_id}")
        print(f"  - Total: ${total:.2f}")
        print("  - ACTION: Sending email confirmation to user.")

    elif event == "order_status_updated":
        order_id = data.get("order_id")
        new_status = data.get("new_status")
        print(f"[EVENT]: Order Status Updated")
        print(f"  - Order ID: {order_id}")
        print(f"  - New Status: {new_status}")
        print(f"  - ACTION: Sending push notification to user.")
    else:
        print(f"[EVENT]: Unknown event received")
        print(f"  - Data: {data}")
    
    print("---------------------------------------------")

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "notification-service"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3005)
