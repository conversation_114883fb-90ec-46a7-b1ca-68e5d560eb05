from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
import uuid
import os
import json
import redis
from sqlalchemy import create_engine, Column, String, Text, Float, Boolean, DateTime, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from dotenv import load_dotenv

load_dotenv()

# Database Configuration
DATABASE_URL = os.getenv("DATABASE_URL", "**************************************************")
REDIS_URL = os.getenv("REDIS_URL", "redis://redis:6379/0")

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Redis Setup
redis_client = redis.from_url(REDIS_URL, decode_responses=True)

# SQLAlchemy Models
class MenuItemDB(Base):
    __tablename__ = "menu_items"
    id = Column(String, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(Text)
    price = Column(Float)
    category = Column(String, index=True)
    available = Column(Boolean, default=True)
    image_url = Column(String, nullable=True)

class PromotionDB(Base):
    __tablename__ = "promotions"
    id = Column(String, primary_key=True, index=True)
    name = Column(String)
    description = Column(Text)
    discount_percentage = Column(Float)
    menu_item_ids = Column(ARRAY(String))
    active = Column(Boolean, default=True)
    start_date = Column(DateTime)
    end_date = Column(DateTime)

Base.metadata.create_all(bind=engine)

app = FastAPI(title="Menu Service", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic Models
class MenuItem(BaseModel):
    id: str
    name: str
    description: str
    price: float
    category: str
    available: bool = True
    image_url: Optional[str] = None
    class Config: orm_mode = True

class MenuItemCreate(BaseModel):
    name: str
    description: str
    price: float
    category: str
    available: bool = True
    image_url: Optional[str] = None

class Promotion(BaseModel):
    id: str
    name: str
    description: str
    discount_percentage: float
    menu_item_ids: List[str]
    active: bool = True
    start_date: datetime
    end_date: datetime
    class Config: orm_mode = True

class PromotionCreate(BaseModel):
    name: str
    description: str
    discount_percentage: float
    menu_item_ids: List[str]
    active: bool = True
    start_date: datetime
    end_date: datetime

# DB Dependency
def get_db():
    db = SessionLocal()
    try: yield db
    finally: db.close()

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "menu-service"}

# Menu Items CRUD
@app.post("/menu/items", response_model=MenuItem)
async def create_menu_item(item: MenuItemCreate, db: Session = Depends(get_db)):
    item_id = str(uuid.uuid4())
    db_item = MenuItemDB(id=item_id, **item.model_dump())
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    redis_client.delete("menu_items_cache")
    return db_item

@app.get("/menu/items", response_model=List[MenuItem])
async def get_menu_items(available_only: bool = False, db: Session = Depends(get_db)):
    cache_key = "menu_items_cache"
    if not available_only:
        cached_items = redis_client.get(cache_key)
        if cached_items:
            return json.loads(cached_items)

    query = db.query(MenuItemDB)
    if available_only:
        query = query.filter(MenuItemDB.available == True)
    
    items = query.all()

    if not available_only:
        items_json = json.dumps([MenuItem.from_orm(item).model_dump() for item in items], default=str)
        redis_client.set(cache_key, items_json, ex=3600) # Cache for 1 hour

    return items

@app.get("/menu/items/{item_id}", response_model=MenuItem)
async def get_menu_item(item_id: str, db: Session = Depends(get_db)):
    db_item = db.query(MenuItemDB).filter(MenuItemDB.id == item_id).first()
    if not db_item:
        raise HTTPException(status_code=404, detail="Menu item not found")
    return db_item

@app.put("/menu/items/{item_id}", response_model=MenuItem)
async def update_menu_item(item_id: str, item_update: MenuItemCreate, db: Session = Depends(get_db)):
    db_item = db.query(MenuItemDB).filter(MenuItemDB.id == item_id).first()
    if not db_item:
        raise HTTPException(status_code=404, detail="Menu item not found")
    update_data = item_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_item, key, value)
    db.commit()
    db.refresh(db_item)
    redis_client.delete("menu_items_cache")
    return db_item

@app.delete("/menu/items/{item_id}")
async def delete_menu_item(item_id: str, db: Session = Depends(get_db)):
    db_item = db.query(MenuItemDB).filter(MenuItemDB.id == item_id).first()
    if not db_item:
        raise HTTPException(status_code=404, detail="Menu item not found")
    db.delete(db_item)
    db.commit()
    redis_client.delete("menu_items_cache")
    return {"message": "Menu item deleted successfully"}

# Promotions CRUD
@app.post("/promotions", response_model=Promotion)
async def create_promotion(promotion: PromotionCreate, db: Session = Depends(get_db)):
    promo_id = str(uuid.uuid4())
    db_promo = PromotionDB(id=promo_id, **promotion.model_dump())
    db.add(db_promo)
    db.commit()
    db.refresh(db_promo)
    return db_promo

@app.get("/promotions", response_model=List[Promotion])
async def get_promotions(active_only: bool = False, db: Session = Depends(get_db)):
    query = db.query(PromotionDB)
    if active_only:
        now = datetime.now()
        query = query.filter(PromotionDB.active == True, PromotionDB.start_date <= now, PromotionDB.end_date >= now)
    return query.all()

@app.get("/promotions/{promotion_id}", response_model=Promotion)
async def get_promotion(promotion_id: str, db: Session = Depends(get_db)):
    db_promo = db.query(PromotionDB).filter(PromotionDB.id == promotion_id).first()
    if not db_promo:
        raise HTTPException(status_code=404, detail="Promotion not found")
    return db_promo

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3002)