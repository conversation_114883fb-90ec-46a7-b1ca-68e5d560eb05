from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
import uuid

app = FastAPI(title="Menu Service", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic Models
class MenuItem(BaseModel):
    id: str
    name: str
    description: str
    price: float
    category: str
    available: bool = True
    image_url: Optional[str] = None

class MenuItemCreate(BaseModel):
    name: str
    description: str
    price: float
    category: str
    available: bool = True
    image_url: Optional[str] = None

class Promotion(BaseModel):
    id: str
    name: str
    description: str
    discount_percentage: float
    menu_item_ids: List[str]
    active: bool = True
    start_date: datetime
    end_date: datetime

class PromotionCreate(BaseModel):
    name: str
    description: str
    discount_percentage: float
    menu_item_ids: List[str]
    active: bool = True
    start_date: datetime
    end_date: datetime

# In-memory storage
menu_items_db = {}
promotions_db = {}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "menu-service"}

# Menu Items CRUD
@app.post("/menu/items", response_model=MenuItem)
async def create_menu_item(item: MenuItemCreate):
    item_id = str(uuid.uuid4())
    menu_item = MenuItem(id=item_id, **item.model_dump())
    menu_items_db[item_id] = menu_item.model_dump()
    return menu_item

@app.get("/menu/items", response_model=List[MenuItem])
async def get_menu_items(available_only: bool = False):
    items = list(menu_items_db.values())
    if available_only:
        items = [item for item in items if item["available"]]
    return [MenuItem(**item) for item in items]

@app.get("/menu/items/{item_id}", response_model=MenuItem)
async def get_menu_item(item_id: str):
    if item_id not in menu_items_db:
        raise HTTPException(status_code=404, detail="Menu item not found")
    return MenuItem(**menu_items_db[item_id])

@app.put("/menu/items/{item_id}", response_model=MenuItem)
async def update_menu_item(item_id: str, item_update: MenuItemCreate):
    if item_id not in menu_items_db:
        raise HTTPException(status_code=404, detail="Menu item not found")
    
    updated_item = MenuItem(id=item_id, **item_update.model_dump())
    menu_items_db[item_id] = updated_item.model_dump()
    return updated_item

@app.delete("/menu/items/{item_id}")
async def delete_menu_item(item_id: str):
    if item_id not in menu_items_db:
        raise HTTPException(status_code=404, detail="Menu item not found")
    
    del menu_items_db[item_id]
    return {"message": "Menu item deleted successfully"}

# Promotions CRUD
@app.post("/promotions", response_model=Promotion)
async def create_promotion(promotion: PromotionCreate):
    promotion_id = str(uuid.uuid4())
    new_promotion = Promotion(id=promotion_id, **promotion.model_dump())
    promotions_db[promotion_id] = new_promotion.model_dump()
    return new_promotion

@app.get("/promotions", response_model=List[Promotion])
async def get_promotions(active_only: bool = False):
    promotions = list(promotions_db.values())
    if active_only:
        now = datetime.now()
        promotions = [
            p for p in promotions 
            if p["active"] and p["start_date"] <= now <= p["end_date"]
        ]
    return [Promotion(**promotion) for promotion in promotions]

@app.get("/promotions/{promotion_id}", response_model=Promotion)
async def get_promotion(promotion_id: str):
    if promotion_id not in promotions_db:
        raise HTTPException(status_code=404, detail="Promotion not found")
    return Promotion(**promotions_db[promotion_id])

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3002)
