from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict
from datetime import datetime
from enum import Enum
import uuid
import os
from sqlalchemy import create_engine, Column, String, Float, DateTime, Enum as SAEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from dotenv import load_dotenv

load_dotenv()

# --- Configuration ---
DATABASE_URL = os.getenv("DATABASE_URL", "**************************************************")

# --- Database Setup ---
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# --- Enums ---
class PaymentStatus(str, Enum):
    PENDING = "pendiente"
    PROCESSING = "procesando"
    COMPLETED = "completado"
    FAILED = "fallido"
    REFUNDED = "reembolsado"

class PaymentMethod(str, Enum):
    CREDIT_CARD = "tarjeta_credito"
    DEBIT_CARD = "tarjeta_debito"
    CASH = "efectivo"
    DIGITAL_WALLET = "billetera_digital"

# --- SQLAlchemy Model ---
class PaymentDB(Base):
    __tablename__ = "payments"
    id = Column(String, primary_key=True, index=True)
    order_id = Column(String, index=True)
    user_id = Column(String, index=True)
    amount = Column(Float)
    payment_method = Column(SAEnum(PaymentMethod))
    status = Column(SAEnum(PaymentStatus), default=PaymentStatus.PENDING)
    transaction_id = Column(String, nullable=True)
    payment_details = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

Base.metadata.create_all(bind=engine)

# --- Pydantic Models ---
class PaymentRequest(BaseModel):
    order_id: str
    user_id: str
    amount: float
    payment_method: PaymentMethod
    payment_details: Optional[Dict] = {}

class Payment(BaseModel):
    id: str
    order_id: str
    user_id: str
    amount: float
    payment_method: PaymentMethod
    status: PaymentStatus
    transaction_id: Optional[str] = None
    payment_details: Optional[Dict] = {}
    created_at: datetime
    updated_at: datetime
    class Config: orm_mode = True

class PaymentConfirmation(BaseModel):
    payment_id: str
    transaction_id: str
    status: PaymentStatus

# --- Dependency ---
def get_db():
    db = SessionLocal()
    try: yield db
    finally: db.close()

app = FastAPI(title="Payment Service", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "payment-service"}

@app.post("/payments", response_model=Payment)
async def create_payment(payment_request: PaymentRequest, db: Session = Depends(get_db)):
    payment_id = str(uuid.uuid4())
    transaction_id = str(uuid.uuid4()) # Simulate transaction ID for now

    db_payment = PaymentDB(
        id=payment_id,
        transaction_id=transaction_id,
        **payment_request.model_dump()
    )
    
    # Simulate payment processing
    if db_payment.payment_method == PaymentMethod.CASH:
        db_payment.status = PaymentStatus.COMPLETED
    else:
        db_payment.status = PaymentStatus.PROCESSING

    db.add(db_payment)
    db.commit()
    db.refresh(db_payment)
    return db_payment

@app.get("/payments/{payment_id}", response_model=Payment)
async def get_payment(payment_id: str, db: Session = Depends(get_db)):
    db_payment = db.query(PaymentDB).filter(PaymentDB.id == payment_id).first()
    if not db_payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    return db_payment

@app.get("/payments", response_model=List[Payment])
async def get_payments(
    user_id: Optional[str] = None,
    order_id: Optional[str] = None,
    status: Optional[PaymentStatus] = None,
    db: Session = Depends(get_db)
):
    query = db.query(PaymentDB)
    if user_id: query = query.filter(PaymentDB.user_id == user_id)
    if order_id: query = query.filter(PaymentDB.order_id == order_id)
    if status: query = query.filter(PaymentDB.status == status)
    return query.all()

@app.post("/payments/{payment_id}/confirm")
async def confirm_payment(payment_id: str, confirmation: PaymentConfirmation, db: Session = Depends(get_db)):
    db_payment = db.query(PaymentDB).filter(PaymentDB.id == payment_id).first()
    if not db_payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    
    db_payment.status = confirmation.status
    db_payment.transaction_id = confirmation.transaction_id
    db.commit()
    return {"message": f"Payment {confirmation.status.value}"}

@app.post("/payments/{payment_id}/refund")
async def refund_payment(payment_id: str, db: Session = Depends(get_db)):
    db_payment = db.query(PaymentDB).filter(PaymentDB.id == payment_id).first()
    if not db_payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    if db_payment.status != PaymentStatus.COMPLETED:
        raise HTTPException(status_code=400, detail="Only completed payments can be refunded")
    
    db_payment.status = PaymentStatus.REFUNDED
    db.commit()
    return {"message": "Payment refunded successfully"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3004)