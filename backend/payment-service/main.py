from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from enum import Enum
import uuid

app = FastAPI(title="Payment Service", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Enums
class PaymentStatus(str, Enum):
    PENDING = "pendiente"
    PROCESSING = "procesando"
    COMPLETED = "completado"
    FAILED = "fallido"
    REFUNDED = "reembolsado"

class PaymentMethod(str, Enum):
    CREDIT_CARD = "tarjeta_credito"
    DEBIT_CARD = "tarjeta_debito"
    CASH = "efectivo"
    DIGITAL_WALLET = "billetera_digital"

# Pydantic Models
class PaymentRequest(BaseModel):
    order_id: str
    user_id: str
    amount: float
    payment_method: PaymentMethod
    payment_details: Optional[dict] = {}

class Payment(BaseModel):
    id: str
    order_id: str
    user_id: str
    amount: float
    payment_method: PaymentMethod
    status: PaymentStatus
    transaction_id: Optional[str] = None
    payment_details: Optional[dict] = {}
    created_at: datetime
    updated_at: datetime

class PaymentConfirmation(BaseModel):
    payment_id: str
    transaction_id: str
    status: PaymentStatus

# In-memory storage
payments_db = {}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "payment-service"}

@app.post("/payments", response_model=Payment)
async def create_payment(payment_request: PaymentRequest):
    payment_id = str(uuid.uuid4())
    transaction_id = str(uuid.uuid4())  # Simulate transaction ID
    
    payment = Payment(
        id=payment_id,
        order_id=payment_request.order_id,
        user_id=payment_request.user_id,
        amount=payment_request.amount,
        payment_method=payment_request.payment_method,
        status=PaymentStatus.PENDING,
        transaction_id=transaction_id,
        payment_details=payment_request.payment_details,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    payments_db[payment_id] = payment.model_dump()
    
    # Simulate payment processing
    if payment_request.payment_method == PaymentMethod.CASH:
        payments_db[payment_id]["status"] = PaymentStatus.COMPLETED
    else:
        payments_db[payment_id]["status"] = PaymentStatus.PROCESSING
    
    payments_db[payment_id]["updated_at"] = datetime.now()
    
    return Payment(**payments_db[payment_id])

@app.get("/payments/{payment_id}", response_model=Payment)
async def get_payment(payment_id: str):
    if payment_id not in payments_db:
        raise HTTPException(status_code=404, detail="Payment not found")
    
    return Payment(**payments_db[payment_id])

@app.get("/payments", response_model=List[Payment])
async def get_payments(
    user_id: Optional[str] = None,
    order_id: Optional[str] = None,
    status: Optional[PaymentStatus] = None
):
    payments = list(payments_db.values())
    
    if user_id:
        payments = [p for p in payments if p["user_id"] == user_id]
    
    if order_id:
        payments = [p for p in payments if p["order_id"] == order_id]
    
    if status:
        payments = [p for p in payments if p["status"] == status]
    
    return [Payment(**payment) for payment in payments]

@app.post("/payments/{payment_id}/confirm")
async def confirm_payment(payment_id: str, confirmation: PaymentConfirmation):
    if payment_id not in payments_db:
        raise HTTPException(status_code=404, detail="Payment not found")
    
    payment = payments_db[payment_id]
    payment["status"] = confirmation.status
    payment["transaction_id"] = confirmation.transaction_id
    payment["updated_at"] = datetime.now()
    
    return {"message": f"Payment {confirmation.status}"}

@app.post("/payments/{payment_id}/refund")
async def refund_payment(payment_id: str):
    if payment_id not in payments_db:
        raise HTTPException(status_code=404, detail="Payment not found")
    
    payment = payments_db[payment_id]
    
    if payment["status"] != PaymentStatus.COMPLETED:
        raise HTTPException(
            status_code=400, 
            detail="Only completed payments can be refunded"
        )
    
    payment["status"] = PaymentStatus.REFUNDED
    payment["updated_at"] = datetime.now()
    
    return {"message": "Payment refunded successfully"}

@app.get("/payments/order/{order_id}", response_model=List[Payment])
async def get_payments_by_order(order_id: str):
    payments = [
        Payment(**payment) 
        for payment in payments_db.values() 
        if payment["order_id"] == order_id
    ]
    return payments

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3004)
