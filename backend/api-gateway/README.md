# API Gateway - Sistema de Pedidos

## 🎯 Descripción
Gateway centralizado que actúa como punto de entrada único para todos los microservicios del sistema de pedidos.

## 🚀 Características
- **Proxy inteligente** - <PERSON><PERSON><PERSON> requests a los microservicios correspondientes
- **Health checks** - Monitorea el estado de todos los servicios
- **CORS configurado** - <PERSON><PERSON> requests desde aplicaciones frontend
- **Logging centralizado** - Registra todas las operaciones
- **Manejo de errores** - Respuestas consistentes para errores de conexión

## 📡 Endpoints Principales

### Gateway
- `GET /` - Información del gateway
- `GET /health` - Estado del gateway y todos los servicios
- `GET /docs` - Documentación interactiva

### Proxy a Microservicios
- `GET|POST|PUT|DELETE /api/users/*` → User Service (3001)
- `GET|POST|PUT|DELETE /api/menu/*` → Menu Service (3002)  
- `GET|POST|PUT|DELETE /api/orders/*` → Order Service (3003)
- `GET|POST|PUT|DELETE /api/payments/*` → Payment Service (3004)

## 🔧 Configuración

### Variables de Entorno
```bash
# Puertos de servicios (por defecto)
USER_SERVICE_URL=http://localhost:3001
MENU_SERVICE_URL=http://localhost:3002
ORDER_SERVICE_URL=http://localhost:3003
PAYMENT_SERVICE_URL=http://localhost:3004
```

### Ejecución Local
```bash
# Instalar dependencias
pip install -r requirements.txt

# Ejecutar
python main.py
```

### Ejecución con Docker
```bash
# Construir imagen
docker build -t api-gateway .

# Ejecutar contenedor
docker run -p 3000:3000 api-gateway
```

## 🧪 Ejemplos de Uso

### Health Check
```bash
curl http://localhost:3000/health
```

### Crear Usuario (a través del gateway)
```bash
curl -X POST http://localhost:3000/api/users/profile \
  -H "Content-Type: application/json" \
  -d '{"user_id":"test123","email":"<EMAIL>","name":"Test User"}'
```

### Obtener Menú (a través del gateway)
```bash
curl http://localhost:3000/api/menu/items
```

## 🔍 Monitoreo
- **Puerto**: 3000
- **Documentación**: http://localhost:3000/docs
- **Health Check**: http://localhost:3000/health

## 🏗️ Arquitectura
```
Frontend Apps → API Gateway (3000) → Microservicios
                     ↓
              ┌─────────────────┐
              │   User (3001)   │
              │   Menu (3002)   │
              │  Order (3003)   │
              │ Payment (3004)  │
              └─────────────────┘
```
