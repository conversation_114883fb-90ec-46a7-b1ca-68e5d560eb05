from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import httpx
import asyncio
from typing import Dict, Any
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="API Gateway - Sistema de Pedidos",
    description="Gateway centralizado para todos los microservicios",
    version="1.0.0"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # En producción, especificar dominios exactos
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuración de servicios
SERVICES = {
    "user": "http://localhost:3001",
    "menu": "http://localhost:3002", 
    "order": "http://localhost:3003",
    "payment": "http://localhost:3004"
}

# Cliente HTTP reutilizable
http_client = httpx.AsyncClient(timeout=30.0)

@app.on_event("startup")
async def startup_event():
    logger.info("🚀 API Gateway iniciado")
    logger.info(f"📋 Servicios configurados: {list(SERVICES.keys())}")

@app.on_event("shutdown")
async def shutdown_event():
    await http_client.aclose()
    logger.info("🔴 API Gateway detenido")

@app.get("/")
async def root():
    return {
        "message": "API Gateway - Sistema de Pedidos",
        "version": "1.0.0",
        "services": list(SERVICES.keys()),
        "status": "active"
    }

@app.get("/health")
async def health_check():
    """Health check del API Gateway y todos los servicios"""
    results = {"gateway": "healthy", "services": {}}
    
    for service_name, service_url in SERVICES.items():
        try:
            response = await http_client.get(f"{service_url}/health", timeout=5.0)
            if response.status_code == 200:
                results["services"][service_name] = "healthy"
            else:
                results["services"][service_name] = "unhealthy"
        except Exception as e:
            results["services"][service_name] = f"error: {str(e)}"
    
    return results

async def proxy_request(service_name: str, path: str, method: str, request: Request):
    """Función para hacer proxy de requests a los microservicios"""
    if service_name not in SERVICES:
        raise HTTPException(status_code=404, detail=f"Servicio '{service_name}' no encontrado")
    
    service_url = SERVICES[service_name]
    target_url = f"{service_url}{path}"
    
    # Obtener headers y body del request original
    headers = dict(request.headers)
    # Remover headers que pueden causar problemas
    headers.pop("host", None)
    headers.pop("content-length", None)
    
    try:
        # Obtener el body si existe
        body = None
        if method.upper() in ["POST", "PUT", "PATCH"]:
            body = await request.body()
        
        # Hacer el request al microservicio
        response = await http_client.request(
            method=method,
            url=target_url,
            headers=headers,
            content=body,
            params=request.query_params
        )
        
        # Retornar la respuesta
        return JSONResponse(
            content=response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text,
            status_code=response.status_code,
            headers=dict(response.headers)
        )
        
    except httpx.TimeoutException:
        logger.error(f"Timeout al conectar con {service_name}: {target_url}")
        raise HTTPException(status_code=504, detail=f"Timeout al conectar con el servicio {service_name}")
    except httpx.ConnectError:
        logger.error(f"Error de conexión con {service_name}: {target_url}")
        raise HTTPException(status_code=503, detail=f"Servicio {service_name} no disponible")
    except Exception as e:
        logger.error(f"Error inesperado con {service_name}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error interno del gateway: {str(e)}")

# Rutas para User Service
@app.api_route("/api/users/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def user_service_proxy(path: str, request: Request):
    return await proxy_request("user", f"/{path}", request.method, request)

# Rutas para Menu Service  
@app.api_route("/api/menu/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def menu_service_proxy(path: str, request: Request):
    return await proxy_request("menu", f"/{path}", request.method, request)

# Rutas para Order Service
@app.api_route("/api/orders/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def order_service_proxy(path: str, request: Request):
    return await proxy_request("order", f"/{path}", request.method, request)

# Rutas para Payment Service
@app.api_route("/api/payments/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def payment_service_proxy(path: str, request: Request):
    return await proxy_request("payment", f"/{path}", request.method, request)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3000)
