{"compilerOptions": {"target": "esnext", "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "nodenext", "moduleResolution": "nodenext", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"]}, "lib": ["dom", "dom.iterable", "esnext"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}