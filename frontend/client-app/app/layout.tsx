import type { Metadata } from 'next'
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs'
import Navigation from '@/components/Navigation'
import './globals.css'

export const metadata: Metadata = {
  title: 'Pedidos App - Cliente',
  description: 'Aplicación de pedidos de comida rápida',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkProvider>
      <html lang="es">
        <body>
          <Navigation />
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}
