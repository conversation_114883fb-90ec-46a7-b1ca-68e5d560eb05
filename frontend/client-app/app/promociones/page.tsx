'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useUser } from '@clerk/nextjs'

interface Promotion {
  id: number
  title: string
  description: string
  discount: string
  validUntil: string
  code: string
  image?: string
  active: boolean
}

export default function PromocionesPage() {
  const { isSignedIn } = useUser()
  const [copiedCode, setCopiedCode] = useState<string | null>(null)

  const promotions: Promotion[] = [
    {
      id: 1,
      title: '2x1 en Hamburguesas',
      description: 'Lleva dos hamburguesas clásicas por el precio de una. Válido de lunes a miércoles.',
      discount: '50% OFF',
      validUntil: '2024-12-31',
      code: 'BURGER2X1',
      active: true
    },
    {
      id: 2,
      title: 'Pizza Familiar + Bebida Gratis',
      description: 'Ordena cualquier pizza familiar y recibe una bebida de 2L completamente gratis.',
      discount: 'Bebid<PERSON> Gratis',
      validUntil: '2024-12-25',
      code: 'PIZZADRINK',
      active: true
    },
    {
      id: 3,
      title: 'Descuento Estudiante',
      description: 'Estudiantes universitarios obtienen 20% de descuento presentando credencial.',
      discount: '20% OFF',
      validUntil: '2024-12-31',
      code: 'STUDENT20',
      active: true
    },
    {
      id: 4,
      title: 'Combo Familiar',
      description: '2 hamburguesas + 2 papas + 2 bebidas por solo $25.99. Perfecto para compartir.',
      discount: '$25.99',
      validUntil: '2024-11-30',
      code: 'FAMILY25',
      active: false
    },
    {
      id: 5,
      title: 'Delivery Gratis',
      description: 'Envío gratuito en pedidos superiores a $20. Válido en toda la ciudad.',
      discount: 'Envío Gratis',
      validUntil: '2024-12-31',
      code: 'FREEDELIVERY',
      active: true
    }
  ]

  const copyCode = (code: string) => {
    navigator.clipboard.writeText(code)
    setCopiedCode(code)
    setTimeout(() => setCopiedCode(null), 2000)
  }

  const activePromotions = promotions.filter(promo => promo.active)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🎉 Promociones Especiales</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Aprovecha nuestras increíbles ofertas y ahorra en tus platillos favoritos
          </p>
        </div>

        {/* Promociones Activas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {activePromotions.map(promo => (
            <div key={promo.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              {/* Badge de descuento */}
              <div className="relative">
                <div className="h-48 bg-gradient-to-br from-red-500 to-orange-500 flex items-center justify-center">
                  <span className="text-white text-6xl">🍔</span>
                </div>
                <div className="absolute top-4 right-4 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full font-bold text-sm">
                  {promo.discount}
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">{promo.title}</h3>
                <p className="text-gray-600 mb-4">{promo.description}</p>
                
                <div className="flex items-center justify-between mb-4">
                  <span className="text-sm text-gray-500">
                    Válido hasta: {new Date(promo.validUntil).toLocaleDateString('es-ES')}
                  </span>
                </div>

                {/* Código de promoción */}
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-xs text-gray-500 uppercase tracking-wide">Código</span>
                      <div className="font-mono font-bold text-lg text-gray-900">{promo.code}</div>
                    </div>
                    <button
                      onClick={() => copyCode(promo.code)}
                      className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
                    >
                      {copiedCode === promo.code ? '✓ Copiado' : 'Copiar'}
                    </button>
                  </div>
                </div>

                {/* Botón de acción */}
                <Link
                  href="/menu"
                  className="w-full bg-red-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-red-700 transition-colors text-center block"
                >
                  Ordenar Ahora
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Promociones Expiradas */}
        {promotions.filter(p => !p.active).length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Promociones Expiradas</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {promotions.filter(p => !p.active).map(promo => (
                <div key={promo.id} className="bg-gray-100 rounded-lg shadow-md overflow-hidden opacity-60">
                  <div className="h-32 bg-gray-300 flex items-center justify-center">
                    <span className="text-gray-500 text-4xl">🍔</span>
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-bold text-gray-700 mb-2">{promo.title}</h3>
                    <p className="text-gray-500 text-sm mb-2">{promo.description}</p>
                    <span className="text-xs text-red-500 font-medium">EXPIRADA</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-red-600 to-orange-600 rounded-lg p-8 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">¿No tienes cuenta aún?</h2>
          <p className="text-xl mb-6">Regístrate y recibe promociones exclusivas directamente en tu email</p>
          {!isSignedIn ? (
            <Link
              href="/sign-up"
              className="bg-white text-red-600 px-8 py-3 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors inline-block"
            >
              Registrarse Gratis
            </Link>
          ) : (
            <Link
              href="/menu"
              className="bg-white text-red-600 px-8 py-3 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors inline-block"
            >
              Ver Menú Completo
            </Link>
          )}
        </div>
      </div>
    </div>
  )
}
