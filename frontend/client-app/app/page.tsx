import Link from 'next/link'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-red-600 to-orange-600 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="text-center">
            <h1 className="text-5xl font-bold mb-6">
              ¡Comida Deliciosa a Domicilio!
            </h1>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              Ordena tus platillos favoritos desde la comodidad de tu hogar.
              Entrega rápida y comida fresca garantizada.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/menu"
                className="bg-white text-red-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors"
              >
                Ver <PERSON>ú
              </Link>
              <Link
                href="/promociones"
                className="bg-yellow-500 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-yellow-600 transition-colors"
              >
                Ver Promociones
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">¿Por qué elegirnos?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Entrega Rápida</h3>
              <p className="text-gray-600">Entregamos tu pedido en 30-45 minutos o menos</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Calidad Garantizada</h3>
              <p className="text-gray-600">Ingredientes frescos y preparación cuidadosa</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Precios Justos</h3>
              <p className="text-gray-600">Los mejores precios sin comprometer la calidad</p>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Categories */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Categorías Populares</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <Link href="/menu" className="group">
              <div className="bg-red-50 rounded-lg p-6 text-center hover:bg-red-100 transition-colors">
                <div className="w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-red-300 transition-colors">
                  <span className="text-2xl">🍕</span>
                </div>
                <h3 className="font-semibold">Pizzas</h3>
              </div>
            </Link>
            <Link href="/menu" className="group">
              <div className="bg-yellow-50 rounded-lg p-6 text-center hover:bg-yellow-100 transition-colors">
                <div className="w-16 h-16 bg-yellow-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-yellow-300 transition-colors">
                  <span className="text-2xl">🍔</span>
                </div>
                <h3 className="font-semibold">Hamburguesas</h3>
              </div>
            </Link>
            <Link href="/menu" className="group">
              <div className="bg-blue-50 rounded-lg p-6 text-center hover:bg-blue-100 transition-colors">
                <div className="w-16 h-16 bg-blue-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-300 transition-colors">
                  <span className="text-2xl">🥤</span>
                </div>
                <h3 className="font-semibold">Bebidas</h3>
              </div>
            </Link>
            <Link href="/menu" className="group">
              <div className="bg-purple-50 rounded-lg p-6 text-center hover:bg-purple-100 transition-colors">
                <div className="w-16 h-16 bg-purple-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-300 transition-colors">
                  <span className="text-2xl">🍰</span>
                </div>
                <h3 className="font-semibold">Postres</h3>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">¿Listo para ordenar?</h2>
          <p className="text-xl mb-8">Explora nuestro menú completo y encuentra tus platillos favoritos</p>
          <Link
            href="/menu"
            className="bg-red-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-red-700 transition-colors inline-block"
          >
            Ordenar Ahora
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">Pedidos App</h3>
              <p className="text-gray-400">La mejor comida a domicilio de la ciudad</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Enlaces Rápidos</h3>
              <ul className="space-y-2">
                <li><Link href="/menu" className="text-gray-400 hover:text-white">Menú</Link></li>
                <li><Link href="/promociones" className="text-gray-400 hover:text-white">Promociones</Link></li>
                <li><Link href="/carrito" className="text-gray-400 hover:text-white">Carrito</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Contacto</h3>
              <p className="text-gray-400">📞 (555) 123-4567</p>
              <p className="text-gray-400">📧 <EMAIL></p>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-8 pt-8 text-center">
            <p className="text-gray-400">&copy; 2024 Pedidos App. Todos los derechos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
