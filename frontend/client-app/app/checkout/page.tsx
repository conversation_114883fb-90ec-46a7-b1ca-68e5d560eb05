
"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';

interface CartItem {
  menu_item_id: string;
  quantity: number;
  customizations: { [key: string]: string };
  name?: string;
  price?: number;
}

export default function CheckoutPage() {
  const { userId } = useAuth();
  const router = useRouter();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('credit_card');

  useEffect(() => {
    if (!userId) {
      setLoading(false);
      return;
    }

    const fetchCart = async () => {
      try {
        const cartResponse = await fetch(`http://localhost:3003/cart/${userId}`);
        const cartData = await cartResponse.json();

        const itemsWithDetails = await Promise.all(
          cartData.items.map(async (item: CartItem) => {
            const productResponse = await fetch(`http://localhost:3002/menu/items/${item.menu_item_id}`);
            const productData = await productResponse.json();
            return { ...item, name: productData.name, price: productData.price };
          })
        );
        setCartItems(itemsWithDetails);
      } catch (error) {
        console.error("Error fetching cart:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCart();
  }, [userId]);

  const total = cartItems.reduce((sum, item) => sum + (item.price || 0) * item.quantity, 0);

  const handlePayment = async () => {
    if (!userId || cartItems.length === 0) return;

    setIsProcessing(true);

    const orderItems = cartItems.map(item => ({
      menu_item_id: item.menu_item_id,
      quantity: item.quantity,
      customizations: item.customizations,
      unit_price: item.price || 0,
    }));

    try {
      // 1. Create Order
      const orderResponse = await fetch('http://localhost:3003/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: userId, items: orderItems }),
      });

      if (!orderResponse.ok) throw new Error('Failed to create order');
      const order = await orderResponse.json();

      // 2. Simulate Payment
      const paymentResponse = await fetch('http://localhost:3004/payments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          order_id: order.id,
          user_id: userId,
          amount: total,
          payment_method: paymentMethod,
          payment_details: { info: 'Pago simulado' },
        }),
      });

      if (!paymentResponse.ok) throw new Error('Payment simulation failed');
      
      // 3. Redirect to confirmation
      router.push(`/confirmacion?orderId=${order.id}`);

    } catch (error) {
      console.error("Payment process failed:", error);
      alert("Hubo un error al procesar tu pedido. Por favor, inténtalo de nuevo.");
    } finally {
      setIsProcessing(false);
    }
  };

  if (loading) return <p>Cargando...</p>;

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Finalizar Pedido</h1>
      <div className="bg-white shadow-lg rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-4">Detalles del Pedido</h2>
        <div className="mb-4">
          {cartItems.map((item, index) => (
            <p key={index} className="text-gray-700">{item.name} ({item.quantity}x): ${(item.price || 0).toFixed(2)}</p>
          ))}
        </div>
        <div className="border-t pt-4">
          <p className="text-xl font-bold">Total: ${total.toFixed(2)}</p>
        </div>
        
        <h2 className="text-2xl font-bold mt-6 mb-4">Método de Pago</h2>
        <div className="mb-4">
          {/* Payment method selection UI */}
        </div>

        <button onClick={handlePayment} disabled={isProcessing || cartItems.length === 0} className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded w-full disabled:bg-gray-400">
          {isProcessing ? 'Procesando...' : 'Pagar Ahora'}
        </button>
      </div>
    </div>
  );
}
