"use client";

import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useAuth } from '@clerk/nextjs';

interface CartItem {
  menu_item_id: string;
  quantity: number;
  customizations: { [key: string]: string };
  // Estos campos se añadirán después de obtener los detalles del producto
  name?: string;
  price?: number;
}

interface Product {
  id: string;
  name: string;
  price: number;
}

export default function CarritoPage() {
  const { userId } = useAuth();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setLoading(false);
      return;
    }

    const fetchCart = async () => {
      try {
        const cartResponse = await fetch(`http://localhost:3003/cart/${userId}`);
        if (!cartResponse.ok) {
          throw new Error('No se pudo obtener el carrito');
        }
        const cartData = await cartResponse.json();
        
        // Obtener detalles de cada producto en el carrito
        const itemsWithDetails = await Promise.all(
          cartData.items.map(async (item: CartItem) => {
            try {
              const productResponse = await fetch(`http://localhost:3002/menu/items/${item.menu_item_id}`);
              if (!productResponse.ok) {
                console.warn(`No se pudo obtener el producto con ID: ${item.menu_item_id}`);
                return { ...item, name: 'Producto no disponible', price: 0 };
              }
              const productData: Product = await productResponse.json();
              return { ...item, name: productData.name, price: productData.price };
            } catch (e) {
              console.error(`Error obteniendo el producto ${item.menu_item_id}:`, e);
              return { ...item, name: 'Error al cargar', price: 0 };
            }
          })
        );
        
        setCartItems(itemsWithDetails);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Ocurrió un error desconocido');
      } finally {
        setLoading(false);
      }
    };

    fetchCart();
  }, [userId]);

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price || 0) * item.quantity, 0);
  const total = subtotal;

  if (loading) {
    return <div className="container mx-auto p-4"><p>Cargando carrito...</p></div>;
  }

  if (error) {
    return <div className="container mx-auto p-4"><p className="text-red-500">{error}</p></div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Tu Carrito</h1>
      {
        cartItems.length === 0 ? (
          <p className="text-gray-600">Tu carrito está vacío.</p>
        ) : (
          <div className="bg-white shadow-lg rounded-lg p-6">
            <div className="mb-4">
              {cartItems.map((item, index) => (
                <div key={index} className="flex justify-between items-center border-b pb-2 mb-2">
                  <div>
                    <h3 className="font-bold text-lg">{item.name || 'Cargando...'}</h3>
                    <p className="text-gray-600">{item.quantity} x ${item.price?.toFixed(2) || 'N/A'}</p>
                    {item.customizations && Object.keys(item.customizations).length > 0 && (
                      <p className="text-sm text-gray-500">
                        Personalización: {Object.entries(item.customizations).map(([key, value]) => `${key}: ${value}`).join(', ')}
                      </p>
                    )}
                  </div>
                  <span className="font-bold">${((item.price || 0) * item.quantity).toFixed(2)}</span>
                </div>
              ))}
            </div>
            <div className="border-t pt-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-lg">Subtotal:</span>
                <span className="font-bold text-lg">${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center mb-4">
                <span className="text-xl font-bold">Total:</span>
                <span className="font-bold text-xl">${total.toFixed(2)}</span>
              </div>
              <Link href="/checkout" className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded w-full text-center block">
                Proceder al Pago
              </Link>
            </div>
          </div>
        )
      }
    </div>
  );
}