import ProductCard from "@/components/ProductCard";

export default function MenuPage() {
  const products = [
    { id: '1', name: 'Hamburguesa Clásica', description: 'Carne, lechuga, tomate, queso', price: 8.50, imageUrl: '/images/hamburguesa.jpg' },
    { id: '2', name: 'Pizza Pepperoni', description: 'Pepperoni, mozzarella, salsa de tomate', price: 12.00, imageUrl: '/images/pizza.jpg' },
    { id: '3', name: 'Papas Fritas', description: 'Crujientes papas fritas con sal', price: 3.00, imageUrl: '/images/papas.jpg' },
  ];

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Nuestro Menú</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.map(product => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
}