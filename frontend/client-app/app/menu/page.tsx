'use client'

import { useState, useEffect } from 'react'
import { useUser } from '@clerk/nextjs'

interface MenuItem {
  id: number
  name: string
  description: string
  price: number
  category: string
  image?: string
  available: boolean
}

export default function MenuPage() {
  const { isSignedIn } = useUser()
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [cart, setCart] = useState<{[key: number]: number}>({})

  const categories = [
    { id: 'all', name: 'Todos' },
    { id: 'burgers', name: 'Hamburguesas' },
    { id: 'pizza', name: 'Pizzas' },
    { id: 'drinks', name: 'Bebidas' },
    { id: 'desserts', name: 'Postres' }
  ]

  // Datos de ejemplo del menú
  const sampleMenuItems: MenuItem[] = [
    {
      id: 1,
      name: 'Hamburguesa Clásica',
      description: 'Carne de res, lechuga, tomate, cebolla y salsa especial',
      price: 12.99,
      category: 'burgers',
      available: true
    },
    {
      id: 2,
      name: 'Pizza Margherita',
      description: 'Salsa de tomate, mozzarella fresca y albahaca',
      price: 15.99,
      category: 'pizza',
      available: true
    },
    {
      id: 3,
      name: 'Coca Cola',
      description: 'Bebida refrescante 500ml',
      price: 2.99,
      category: 'drinks',
      available: true
    },
    {
      id: 4,
      name: 'Cheesecake',
      description: 'Delicioso pastel de queso con frutos rojos',
      price: 6.99,
      category: 'desserts',
      available: true
    },
    {
      id: 5,
      name: 'Hamburguesa BBQ',
      description: 'Carne de res, bacon, cebolla caramelizada y salsa BBQ',
      price: 14.99,
      category: 'burgers',
      available: true
    },
    {
      id: 6,
      name: 'Pizza Pepperoni',
      description: 'Salsa de tomate, mozzarella y pepperoni',
      price: 17.99,
      category: 'pizza',
      available: false
    }
  ]

  useEffect(() => {
    // Simular carga de datos
    setTimeout(() => {
      setMenuItems(sampleMenuItems)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredItems = selectedCategory === 'all' 
    ? menuItems 
    : menuItems.filter(item => item.category === selectedCategory)

  const addToCart = (itemId: number) => {
    if (!isSignedIn) {
      alert('Debes iniciar sesión para agregar productos al carrito')
      return
    }
    setCart(prev => ({
      ...prev,
      [itemId]: (prev[itemId] || 0) + 1
    }))
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando menú...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Nuestro Menú</h1>
          <p className="text-xl text-gray-600">Deliciosos platillos preparados con los mejores ingredientes</p>
        </div>

        {/* Filtros de categoría */}
        <div className="flex flex-wrap justify-center gap-4 mb-8">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-2 rounded-full font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-red-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-red-50 border border-gray-300'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Grid de productos */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map(item => (
            <div key={item.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500 text-4xl">🍽️</span>
              </div>
              <div className="p-6">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-xl font-semibold text-gray-900">{item.name}</h3>
                  {!item.available && (
                    <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                      No disponible
                    </span>
                  )}
                </div>
                <p className="text-gray-600 mb-4">{item.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-2xl font-bold text-red-600">${item.price}</span>
                  <button
                    onClick={() => addToCart(item.id)}
                    disabled={!item.available}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      item.available
                        ? 'bg-red-600 text-white hover:bg-red-700'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    {cart[item.id] ? `En carrito (${cart[item.id]})` : 'Agregar'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No hay productos disponibles en esta categoría</p>
          </div>
        )}
      </div>
    </div>
  )
}
