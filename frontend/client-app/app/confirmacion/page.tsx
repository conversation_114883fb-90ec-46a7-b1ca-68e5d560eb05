
"use client";

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';

interface OrderItem {
  menu_item_id: string;
  quantity: number;
  unit_price: number;
  name?: string; // Added to store product name
}

interface Order {
  id: string;
  status: string;
  total_amount: number;
  items: OrderItem[];
}

function ConfirmacionContent() {
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId');
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!orderId) {
      setLoading(false);
      setError("No se proporcionó un ID de pedido.");
      return;
    }

    const fetchOrderDetails = async () => {
      try {
        const response = await fetch(`http://localhost:3003/orders/${orderId}`);
        if (!response.ok) {
          throw new Error('No se pudo obtener la orden');
        }
        const orderData: Order = await response.json();

        const itemsWithDetails = await Promise.all(
          orderData.items.map(async (item) => {
            const productResponse = await fetch(`http://localhost:3002/menu/items/${item.menu_item_id}`);
            if (productResponse.ok) {
              const productData = await productResponse.json();
              return { ...item, name: productData.name };
            }
            return { ...item, name: 'Producto no encontrado' };
          })
        );

        setOrder({ ...orderData, items: itemsWithDetails });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Ocurrió un error desconocido');
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [orderId]);

  if (loading) {
    return <p className="text-center">Cargando confirmación del pedido...</p>;
  }

  if (error) {
    return <p className="text-center text-red-500">Error: {error}</p>;
  }

  if (!order) {
    return <p className="text-center">No se encontraron los detalles del pedido.</p>;
  }

  return (
    <div className="container mx-auto p-4 text-center">
      <h1 className="text-3xl font-bold mb-6 text-green-600">¡Pedido Confirmado!</h1>
      <div className="bg-white shadow-lg rounded-lg p-6 max-w-2xl mx-auto">
        <p className="text-xl text-gray-800 mb-4">Gracias por tu compra. Tu pedido ha sido recibido y está siendo procesado.</p>
        <p className="text-lg text-gray-600">Número de Pedido: <strong>#{order.id.split('-')[0]}</strong></p>
        <p className="text-lg text-gray-600">Estado: <span className="font-semibold capitalize">{order.status}</span></p>
        <p className="text-lg text-gray-600 mb-6">Total: <span className="font-bold">${order.total_amount.toFixed(2)}</span></p>
        
        <div className="text-left my-4 border-t pt-4">
          <h3 className="font-bold text-xl mb-2">Resumen del Pedido:</h3>
          {order.items.map((item, index) => (
            <div key={index} className="flex justify-between items-center mb-1">
              <span>{item.name} (x{item.quantity})</span>
              <span>${(item.unit_price * item.quantity).toFixed(2)}</span>
            </div>
          ))}
        </div>

        <Link href="/menu" className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded inline-block mt-4">
          Volver al Menú
        </Link>
      </div>
    </div>
  );
}

export default function ConfirmacionPage() {
  return (
    <Suspense fallback={<p>Cargando...</p>}>
      <ConfirmacionContent />
    </Suspense>
  );
}
