# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
logs
*.log

# PREVENCIÓN DE DUPLICADOS
# ========================
# Evitar duplicación de directorios app
src/app/
app_backup/
app_old/
app_temp/

# Evitar duplicación de middleware
src/middleware.ts
middleware_backup.ts
middleware_old.ts

# Evitar duplicación de components
src/components/
components_backup/
components_old/

# Archivos temporales de prueba
test-*.js
test-*.ts
*.test.temp.js
*.test.temp.ts

# Backups automáticos
*.backup
*.bak
*.orig
*~
