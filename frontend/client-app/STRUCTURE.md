# Estructura del Proyecto Client-App

## 📁 Estructura Definitiva

```
frontend/client-app/
├── app/                    # Next.js 13+ App Router (ÚNICO)
│   ├── layout.tsx         # Layout principal con ClerkProvider
│   ├── page.tsx           # Página de inicio
│   ├── globals.css        # Estilos globales
│   ├── sign-in/           # Página de inicio de sesión
│   │   └── [[...sign-in]]/
│   │       └── page.tsx
│   └── sign-up/           # Página de registro
│       └── [[...sign-up]]/
│           └── page.tsx
├── components/            # Componentes reutilizables
│   └── Navigation.tsx     # Barra de navegación con Clerk
├── middleware.ts          # Middleware de Clerk (raíz del proyecto)
├── package.json
├── tsconfig.json
├── next.config.js
├── tailwind.config.js
├── postcss.config.js
└── .env.local            # Variables de entorno (NO COMMITEAR)
```

## 🚫 Estructura PROHIBIDA (Evitar Duplicados)

### ❌ NO CREAR:
- `src/app/` - Duplica funcionalidad de `/app`
- `src/middleware.ts` - Duplica funcionalidad de `/middleware.ts`
- `src/components/` - Duplica funcionalidad de `/components`
- `app_backup/`, `app_old/` - Backups manuales
- `components_backup/` - Backups de componentes

## ✅ Reglas de Desarrollo

### 1. **Ubicación de Archivos:**
- **App Router:** Solo en `/app` (raíz)
- **Middleware:** Solo en `/middleware.ts` (raíz)
- **Componentes:** Solo en `/components`
- **Tipos:** En `/types` (si es necesario)
- **Utilidades:** En `/lib` (si es necesario)

### 2. **Imports:**
- Usar alias `@/` que apunta a la raíz del proyecto
- Ejemplo: `import Navigation from '@/components/Navigation'`

### 3. **Clerk Configuration:**
- Variables en `.env.local` (NO COMMITEAR)
- Middleware en `/middleware.ts` (raíz)
- ClerkProvider en `/app/layout.tsx`

## 🔧 Configuración

### TypeScript Paths:
```json
{
  "baseUrl": ".",
  "paths": {
    "@/*": ["./*"]
  }
}
```

### Clerk Middleware:
```typescript
import { clerkMiddleware } from '@clerk/nextjs/server';
export default clerkMiddleware();
```

## 📝 Notas Importantes

1. **Next.js 13+** usa App Router por defecto
2. **Middleware** debe estar en la raíz para funcionar correctamente
3. **Clerk** requiere configuración específica de rutas
4. **NO** mezclar estructura `src/` con estructura de raíz
5. **Siempre** verificar que no existan duplicados antes de crear archivos

## 🚨 En Caso de Problemas

Si encuentras duplicados:
1. Identificar cuál es la versión más reciente
2. Hacer backup de la versión actual
3. Eliminar duplicados
4. Actualizar imports si es necesario
5. Probar funcionamiento

---
**Última actualización:** 2025-01-27
**Estructura validada:** ✅ Funcionando con Clerk
