'use client'

import Link from 'next/link'
import { SignIn<PERSON>utton, SignUpButton, User<PERSON>utton, useUser } from '@clerk/nextjs'

export default function Navigation() {
  const { isSignedIn, user } = useUser()

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link href="/" className="text-2xl font-bold text-red-600">
            Pedidos App
          </Link>

          <div className="hidden md:flex space-x-8">
            <Link href="/" className="text-gray-700 hover:text-red-600 transition-colors">
              Inicio
            </Link>
            <Link href="/menu" className="text-gray-700 hover:text-red-600 transition-colors">
              Menú
            </Link>
            <Link href="/promociones" className="text-gray-700 hover:text-red-600 transition-colors">
              Promociones
            </Link>
            {isSignedIn && (
              <Link href="/carrito" className="text-gray-700 hover:text-red-600 transition-colors">
                Carrito
              </Link>
            )}
          </div>

          <div className="flex items-center space-x-4">
            {isSignedIn ? (
              <>
                <span className="text-sm text-gray-600">
                  Hola, {user?.firstName || user?.emailAddresses[0]?.emailAddress}
                </span>
                <Link
                  href="/carrito"
                  className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                >
                  🛒 Carrito
                </Link>
                <UserButton afterSignOutUrl="/" />
              </>
            ) : (
              <>
                <SignInButton mode="modal">
                  <button className="text-gray-700 hover:text-red-600 transition-colors">
                    Iniciar Sesión
                  </button>
                </SignInButton>
                <SignUpButton mode="modal">
                  <button className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                    Registrarse
                  </button>
                </SignUpButton>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}
