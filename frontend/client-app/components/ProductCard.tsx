"use client";

import Image from 'next/image';
import { useState } from 'react';
import { useAuth } from '@clerk/nextjs';

interface ProductProps {
  product: {
    id: string;
    name: string;
    description: string;
    price: number;
    imageUrl: string;
  };
}

export default function ProductCard({ product }: ProductProps) {
  const { userId } = useAuth();
  const [extraCheese, setExtraCheese] = useState(false);

  const handleAddToCart = async () => {
    if (!userId) {
      alert("Debes iniciar sesión para agregar productos al carrito.");
      return;
    }

    const cartItem = {
      menu_item_id: product.id,
      quantity: 1,
      customizations: {
        extra_queso: extraCheese.toString(),
      },
    };

    try {
      const response = await fetch(`http://localhost:3003/cart/${userId}/items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cartItem),
      });

      if (response.ok) {
        alert(`${product.name} ha sido agregado al carrito.`);
        console.log("Producto agregado al carrito:", await response.json());
      } else {
        const errorData = await response.json();
        alert(`Error al agregar el producto: ${errorData.detail || 'Error desconocido'}`);
        console.error("Error al agregar al carrito:", errorData);
      }
    } catch (error) {
      alert("Hubo un problema de conexión. Inténtalo de nuevo.");
      console.error("Error de red:", error);
    }
  };

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden">
      <Image src={product.imageUrl} alt={product.name} width={400} height={250} className="w-full h-48 object-cover" />
      <div className="p-4">
        <h3 className="font-bold text-xl mb-2">{product.name}</h3>
        <p className="text-gray-600 text-sm mb-4">{product.description}</p>
        <div className="flex items-center justify-between mb-2">
          <span className="font-bold text-lg">${product.price.toFixed(2)}</span>
          <label className="flex items-center">
            <input
              type="checkbox"
              className="form-checkbox h-5 w-5 text-blue-600"
              checked={extraCheese}
              onChange={() => setExtraCheese(!extraCheese)}
            />
            <span className="ml-2 text-gray-700">Extra Cheese</span>
          </label>
        </div>
        <button onClick={handleAddToCart} className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">
          Añadir al Carrito
        </button>
      </div>
    </div>
  );
}