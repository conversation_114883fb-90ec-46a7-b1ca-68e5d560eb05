FROM node:20-alpine AS base

# 1. Install dependencies
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json ./ 
RUN npm ci

# 2. Build the Next.js application
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
ENV NEXT_TELEMETRY_DISABLED 1
ENV NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY pk_test_ZGFybGluZy1wb3Jwb2lzZS00OC5jbGVyay5hY2NvdW50cy5kZXYk
ENV CLERK_SECRET_KEY sk_test_nXo5gU4C18fxfolvqTToV7T4lBTFDccKcfMh8ZMNzy
ENV NEXT_PUBLIC_CLERK_SIGN_IN_URL /sign-in
ENV NEXT_PUBLIC_CLERK_SIGN_UP_URL /sign-up
RUN npm run build

# 3. Run the Next.js application
FROM node:20-alpine AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/.next/standalone ./ 
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/static ./.next/static

USER nextjs

EXPOSE 3006

ENV PORT 3006

CMD ["node", "server.js"]