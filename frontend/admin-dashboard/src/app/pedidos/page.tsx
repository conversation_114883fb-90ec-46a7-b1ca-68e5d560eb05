
"use client";

import { useState, useEffect } from 'react';

interface Order {
  id: string;
  user_id: string;
  status: string;
  total_amount: number;
  items: { name: string; quantity: number }[];
}

export default function PedidosPage() {
  const [orders, setOrders] = useState<Order[]>([]);

  useEffect(() => {
    // Fetch initial orders
    const fetchOrders = async () => {
      try {
        const response = await fetch('http://localhost:3003/orders');
        const data = await response.json();
        setOrders(data);
      } catch (error) {
        console.error("Failed to fetch orders:", error);
      }
    };

    fetchOrders();

    // Setup WebSocket connection
    const ws = new WebSocket('ws://localhost:3003/ws/orders');

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      const { event: eventType, order } = data;

      if (eventType === 'order_created') {
        setOrders(prevOrders => [order, ...prevOrders]);
      } else if (eventType === 'order_status_updated') {
        setOrders(prevOrders => prevOrders.map(o => o.id === order.id ? order : o));
      }
    };

    ws.onclose = () => {
      console.log('WebSocket disconnected');
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    return () => {
      ws.close();
    };
  }, []);

  const changeOrderStatus = async (id: string, newStatus: string) => {
    try {
      await fetch(`http://localhost:3003/orders/${id}/status?status=${newStatus}`, {
        method: 'PUT',
      });
      // The status will be updated via WebSocket, no need to set state here
    } catch (error) {
      console.error("Failed to update order status:", error);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Lista de Pedidos en Tiempo Real</h1>
      <div className="bg-white shadow-lg rounded-lg p-6">
        <table className="min-w-full bg-white">
          <thead>
            <tr>
              <th className="py-2 px-4 border-b">ID Pedido</th>
              <th className="py-2 px-4 border-b">Cliente ID</th>
              <th className="py-2 px-4 border-b">Estado</th>
              <th className="py-2 px-4 border-b">Total</th>
              <th className="py-2 px-4 border-b">Acciones</th>
            </tr>
          </thead>
          <tbody>
            {orders.map(order => (
              <tr key={order.id}>
                <td className="py-2 px-4 border-b">{order.id.substring(0, 8)}</td>
                <td className="py-2 px-4 border-b">{order.user_id.substring(0, 8)}</td>
                <td className="py-2 px-4 border-b capitalize">{order.status}</td>
                <td className="py-2 px-4 border-b">${order.total_amount.toFixed(2)}</td>
                <td className="py-2 px-4 border-b">
                  <button
                    onClick={() => changeOrderStatus(order.id, 'preparando')}
                    className="bg-yellow-500 text-white px-2 py-1 rounded text-sm mr-2 disabled:bg-gray-400"
                    disabled={order.status !== 'pendiente'}
                  >
                    Preparando
                  </button>
                  <button
                    onClick={() => changeOrderStatus(order.id, 'listo')}
                    className="bg-green-500 text-white px-2 py-1 rounded text-sm disabled:bg-gray-400"
                    disabled={order.status !== 'preparando'}
                  >
                    Listo
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
