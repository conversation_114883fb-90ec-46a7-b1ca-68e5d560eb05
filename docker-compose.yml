version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: pedidos_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  # Database Management
  adminer:
    image: adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres

  # API Gateway
  api-gateway:
    build: ./backend/api-gateway
    ports:
      - "3000:3000"
    depends_on:
      - user-service
      - menu-service
      - order-service
      - payment-service

  # Microservices
  user-service:
    build: ./backend/user-service
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis

  menu-service:
    build: ./backend/menu-service
    ports:
      - "3002:3002"
    depends_on:
      - postgres
      - redis

  order-service:
    build: ./backend/order-service
    ports:
      - "3003:3003"
    depends_on:
      - postgres
      - redis

  payment-service:
    build: ./backend/payment-service
    ports:
      - "3004:3004"
    depends_on:
      - postgres
      - redis

volumes:
  postgres_data:
