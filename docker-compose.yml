version: '3.8'

services:
  # Database
  postgres:
    build:
      context: .
      dockerfile: ./postgres/Dockerfile
    environment:
      POSTGRES_DB: pedidos_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - app-network

  # Database Management
  adminer:
    image: adminer
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - app-network

  # Kong Gateway
  kong:
    build: ./kong
    environment:
      KONG_DATABASE: 'off'
      KONG_DECLARATIVE_CONFIG: /usr/local/kong/declarative/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_PLUGINS: bundled
      KONG_ADMIN_LISTEN: 0.0.0.0:8001, 0.0.0.0:8444 ssl
      KONG_PROXY_LISTEN: 0.0.0.0:8000, 0.0.0.0:8443 ssl
    ports:
      - "8000:8000/tcp" # HTTP traffic
      - "8443:8443/tcp" # HTTPS traffic
      - "8001:8001/tcp" # Admin API HTTP
      - "8444:8444/tcp" # Admin API HTTPS
    volumes:
      - ./kong/kong.yml:/usr/local/kong/declarative/kong.yml:ro
    networks:
      - app-network
    

  # Microservices
  user-service:
    build: ./backend/user-service
    ports:
      - "3001:3001"
    environment:
      CLERK_SECRET_KEY: sk_test_nXo5gU4C18fxfolvqTToV7T4lBTFDccKcfMh8ZMNzy
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3001/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3

  menu-service:
    build: ./backend/menu-service
    ports:
      - "3002:3002"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3002/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3

  order-service:
    build: ./backend/order-service
    ports:
      - "3003:3003"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3003/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3

  payment-service:
    build: ./backend/payment-service
    ports:
      - "3004:3004"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3004/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3

  client-app:
    build:
      context: ./frontend/client-app
      dockerfile: Dockerfile
    ports:
      - "3006:3006"
    environment:
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: pk_test_ZGFybGluZy1wb3Jwb2lzZS00OC5jbGVyay5hY2NvdW50cy5kZXYk
      CLERK_SECRET_KEY: sk_test_nXo5gU4C18fxfolvqTToV7T4lBTFDccKcfMh8ZMNzy
      NEXT_PUBLIC_CLERK_SIGN_IN_URL: /sign-in
      NEXT_PUBLIC_CLERK_SIGN_UP_URL: /sign-up
    networks:
      - app-network
    depends_on:
      - user-service
      - menu-service
      - order-service
      - payment-service
      - kong

  notification-service:
    build: ./backend/notification-service
    ports:
      - "3005:3005"
    environment:
      REDIS_URL: redis://redis:6379/0
    depends_on:
      - redis
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3005/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3

  admin-dashboard:
    build:
      context: ./frontend/admin-dashboard
      dockerfile: Dockerfile
    ports:
      - "3007:3006" # Mapea el puerto 3006 del contenedor al 3007 del host
    networks:
      - app-network
    depends_on:
      - user-service
      - menu-service
      - order-service
      - payment-service
      - kong

volumes:
  postgres_data:
  kong_data:

networks:
  app-network:
    driver: bridge
