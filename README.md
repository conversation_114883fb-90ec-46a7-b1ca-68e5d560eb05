# SaaS para Pedidos de Comida Rápida

Este proyecto es una aplicación de software como servicio (SaaS) para gestionar pedidos de comida rápida. Está construido con una arquitectura de microservicios y tecnologías modernas.

## Arquitectura

La aplicación se divide en varios microservicios, cada uno con su propia base de datos PostgreSQL. La comunicación entre los servicios se realiza a través de una API Gateway y un message broker (Redis).

### Stack Tecnológico

- **Backend**: FastAPI (Python)
- **Frontend**: Next.js (React)
- **Base de Datos**: PostgreSQL
- **Cache y Mensajería**: Redis
- **API Gateway**: Kong
- **Autenticación**: Clerk
- **Contenedores**: Docker

### Microservicios

- **User Service**: Gestiona la información de los usuarios.
- **Menu Service**: Gestiona el menú de productos y las promociones.
- **Order Service**: Gestiona los pedidos y el carrito de compras.
- **Payment Service**: Procesa los pagos.
- **Notification Service**: Envía notificaciones (simuladas).

### Interfaces de Usuario

- **Aplicación del Cliente**: Permite a los clientes ver el menú, hacer pedidos y pagar.
- **Dashboard de Administración**: Permite a los administradores ver y gestionar los pedidos en tiempo real.

## Cómo Empezar

### Prerrequisitos

- Docker
- Docker Compose

### Instalación

1. Clona este repositorio:
   ```sh
   git clone <URL_DEL_REPOSITORIO>
   cd <NOMBRE_DEL_DIRECTORIO>
   ```

2. Configura las variables de entorno. Crea un archivo `.env` en la raíz del proyecto y añade las siguientes variables:
   ```
   # Clerk
   CLERK_SECRET_KEY=tu_clerk_secret_key
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=tu_clerk_publishable_key
   NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
   NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up

   # Base de Datos
   DATABASE_URL=**************************************************

   # Redis
   REDIS_URL=redis://redis:6379/0
   ```

### Ejecución

Para levantar todos los servicios, ejecuta el siguiente comando:

```sh
docker-compose up -d
```

Esto levantará todos los servicios en segundo plano. Para ver los logs, ejecuta:

```sh
docker-compose logs -f
```

### Acceso a las Aplicaciones

- **Aplicación del Cliente**: http://localhost:3006
- **Dashboard de Administración**: http://localhost:3007
- **Adminer (Gestión de Base de Datos)**: http://localhost:8080
- **API Gateway (Kong)**: http://localhost:8000

## Desarrollo

Cada microservicio y aplicación de frontend se encuentra en su propio directorio. Puedes encontrar más detalles sobre cada uno en sus respectivos `README.md`.

## Contribuir

Las contribuciones son bienvenidas. Por favor, abre un issue para discutir cualquier cambio que te gustaría hacer.
